"use client";

import { useLayoutEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useMagicLinkAuth } from "@/domain/auth/hooks/use-magic-link-auth";

interface MagicLinkVerifierProps {
  children: React.ReactNode;
}

export default function MagicLinkVerifier({
  children,
}: MagicLinkVerifierProps) {
  const searchParams = useSearchParams();
  const { mutate: verifyMagicToken, isPending } = useMagicLinkAuth();

  useLayoutEffect(() => {
    const hash = window.location.hash.substring(1);
    const params = new URLSearchParams(hash);
    const token = params.get("access_token");
    const type = params.get("type");

    if (token && type === "magiclink") {
      verifyMagicToken(token);
    }
  }, [searchParams, verifyMagicToken]);

  if (isPending) {
    return (
      <div className="min-h-screen relative flex flex-col items-center justify-center bg-gray-50">
        <div className="h-1 bg-[#cc3a1b] animate-pulse absolute z-100 top-0 w-full" />
        <>{children}</>
      </div>
    );
  }

  return <>{children}</>;
}
