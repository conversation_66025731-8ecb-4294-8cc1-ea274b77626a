"use client";

import { redirect } from "next/navigation";
import useGetMe from "../hooks/use-auth-me";

export const AuthGuideWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { data: user, error, isLoading } = useGetMe();

  if (!isLoading && (error || !user)) {
    console.log("I AM RUNNING");
    setTimeout(() => {
      redirect("/auth/signin");
    }, 100);
    return null;
  }

  if (isLoading) {
    return null;
  }

  return <div>{children}</div>;
};
