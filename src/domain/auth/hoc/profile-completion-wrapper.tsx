"use client";

import { useRouter } from "next/navigation";
import useGetMe from "../hooks/use-auth-me";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info } from "lucide-react";

export const ProfileCompletionWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const router = useRouter();
  const { data: user } = useGetMe();

  const isProfileIncomplete =
    user &&
    (!user.first_name ||
      !user.last_name ||
      !user.phone_number ||
      !user.date_of_birth ||
      !user.gender ||
      !user.residential_address);

  if (isProfileIncomplete) {
    return (
      <>
        <div className="px-4 pt-6 lg:px-6">
          <Alert variant="destructive">
            <Info className="h-4 w-4" />
            <AlertTitle className="p-0 m-0">Profile Incomplete</AlertTitle>
            <AlertDescription className="flex items-center justify-between">
              <span>Complete your profile to access all features.</span>
              <Button
                variant="link"
                className="text-primary hover:underline p-0 m-0 h-3 cursor-pointer"
                onClick={() => router.push("/settings/profile")}
              >
                Complete Profile
              </Button>
            </AlertDescription>
          </Alert>
        </div>
        <div>{children}</div>
      </>
    );
  }

  return <div>{children}</div>;
};
