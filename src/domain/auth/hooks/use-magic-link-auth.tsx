import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { setCookie, deleteCookie } from "cookies-next";
import { $http, addAccessTokenToHttpInstance } from "../lib/http";
import { toast } from "sonner";
import useGetMe from "./use-auth-me";

export function useMagicLinkAuth() {
  const queryClient = useQueryClient();
  // eslint-disable-next-line
  const getMeQuery = useGetMe();

  return useMutation({
    mutationFn: async (token: string) => {
      return await $http.post(`/auth/magic-link/verify`, { token });
    },
    onSuccess: async data => {
      const responseData = data.data;
      const access_token = responseData.access_token;
      const userData = responseData.user || responseData.data;

      addAccessTokenToHttpInstance(access_token);

      setCookie("markket_auth_token", access_token, {
        secure: true,
        sameSite: "strict",
        maxAge: 7 * 24 * 60 * 60,
      });

      if (userData) {
        setCookie("markket_user_data", JSON.stringify(userData), {
          secure: true,
          sameSite: "strict",
          maxAge: 7 * 24 * 60 * 60,
        });
        queryClient.setQueryData(["current-user"], { data: userData });
      } else {
        queryClient.invalidateQueries({ queryKey: ["current-user"] });
      }

      toast.success("Welcome to Markket!", {
        description: "You have been successfully signed in.",
        duration: 4000,
      });
      if (window.location.hash) {
        const url = new URL(window.location.href);
        url.hash = "";
        window.history.replaceState({}, "", url.toString());
      }
    },
    onError: error => {
      console.error("Verification error:", error);
      deleteCookie("markket_auth_token");
      deleteCookie("markket_user_data");
      deleteCookie("markket_current_role");

      queryClient.removeQueries({ queryKey: ["current-user"] });

      let errorMessage = "Failed to verify magic link. Please try again.";

      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const message = error.response?.data?.message || error.message;

        if (status === 400) {
          errorMessage = "Invalid magic link token";
        } else if (status === 401) {
          errorMessage = "Magic link has expired";
        } else if (status === 404) {
          errorMessage = "Magic link not found";
        } else {
          errorMessage = `Verification failed: ${message}`;
        }
      }

      toast.error("Verification failed", {
        description: errorMessage,
        duration: 6000,
      });

      if (window.location.hash) {
        const url = new URL(window.location.href);
        url.hash = "";
        window.history.replaceState({}, "", url.toString());
      }
    },
  });
}
