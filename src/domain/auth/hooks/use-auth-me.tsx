import { useQuery } from "@tanstack/react-query";
import { getCookie } from "cookies-next";
import { $http } from "../lib/http";

const useGetMe = () => {
  const queryKey = ["current-user"];

  return useQuery({
    queryKey: queryKey,
    queryFn: async () => {
      const userData = (await $http.get(`/auth/me`)).data;

      const persistedRole = getCookie("markket_current_role");
      if (persistedRole && userData.current_role !== persistedRole) {
        return { ...userData, current_role: persistedRole };
      }

      return userData;
    },
    enabled: !!getCookie("markket_auth_token"),
  });
};

export default useGetMe;
