import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteCookie } from "cookies-next";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export function useLogout() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: async () => {

      deleteCookie("markket_auth_token");
      deleteCookie("markket_user_data");
      deleteCookie("markket_current_role");

      queryClient.clear();
      
      return Promise.resolve();
    },
    onSuccess: () => {
      toast.success("Logged out successfully", {
        description: "You have been signed out of your account.",
        duration: 3000,
      });
      
      router.push("/");
    },
    onError: (error) => {
      console.error("Logout error:", error);
      toast.error("Logout failed", {
        description: "There was an issue signing you out. Please try again.",
      });
    },
  });
}
