import { useMutation } from "@tanstack/react-query";
import { $http, addAccessTokenToHttpInstance } from "../lib/http";
import { toast } from "sonner";

export function useVerifyMagicToken() {
  return useMutation({
    mutationFn: async (token: string) => {
      return await $http.post(`/auth/magic-link/verify`, { token });
    },
    onSuccess: data => {
      addAccessTokenToHttpInstance(data.data.access_token);
    },
    onError: error => {
      console.error("Verification error:", error);
      toast.error("Something went wrong", {
        description: "Failed to send magic link. Please try again.",
      });
    },
  });
}
