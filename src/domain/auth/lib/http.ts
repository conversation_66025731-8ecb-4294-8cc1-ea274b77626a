import { API_BASE } from "@/lib/constants";
import axios from "axios";
import { getCookie } from "cookies-next";

const $http = axios.create({
  baseURL: API_BASE,
  headers: {
    "Content-Type": "application/json",
  },
});

$http.interceptors.request.use(
  config => {
    const token = getCookie("markket_auth_token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

const addAccessTokenToHttpInstance = (token: string) => {
  $http.interceptors.request.use(
    config => {
      config.headers["Authorization"] = `Bearer ${token}`;
      return config;
    },
    error => {
      return Promise.reject(error);
    }
  );
};

export { $http, addAccessTokenToHttpInstance };
