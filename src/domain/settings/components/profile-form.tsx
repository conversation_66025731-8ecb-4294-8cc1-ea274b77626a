"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { useUpdateProfile } from "../hooks/use-update-profile";
import LoadingBars from "@/components/ui/loading-bars";

interface ProfileFormProps {
  initialData: USER;
}

export default function ProfileForm({ initialData }: ProfileFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: initialData,
  });
  const { mutate: updateProfile, isPending } = useUpdateProfile();

  const onSubmit = (data: USER) => {
    updateProfile(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="mb-8">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">
          Personal Information
        </h2>

        {Object.keys(errors).length > 0 && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>
              <ul className="list-disc pl-5 space-y-1">
                {errors.first_name && <li>{errors.first_name.message}</li>}
                {errors.last_name && <li>{errors.last_name.message}</li>}
                {errors.phone_number && <li>{errors.phone_number.message}</li>}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label
              htmlFor="first_name"
              className="text-sm font-medium text-gray-700"
            >
              First Name
            </Label>
            <Input
              id="first_name"
              type="text"
              placeholder="Enter your first name"
              {...register("first_name", {
                required: "First name is required",
              })}
              className={`w-full ${errors.first_name ? "border-red-500" : ""}`}
            />
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="last_name"
              className="text-sm font-medium text-gray-700"
            >
              Last Name
            </Label>
            <Input
              id="last_name"
              placeholder="Enter your last name"
              type="text"
              {...register("last_name", { required: "Last name is required" })}
              className={`w-full ${errors.last_name ? "border-red-500" : ""}`}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div className="space-y-2">
            <Label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              Email
            </Label>
            <Input
              id="email"
              type="email"
              {...register("email")}
              className="w-full"
              disabled
            />
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="phone_number"
              className="text-sm font-medium text-gray-700"
            >
              Phone Number
            </Label>
            <Input
              id="phone_number"
              type="tel"
              placeholder="Enter your phone number"
              {...register("phone_number", {
                pattern: {
                  value: /^[0-9+-]+$/,
                  message: "Please enter a valid phone number",
                },
              })}
              className={`w-full ${errors.phone_number ? "border-red-500" : ""}`}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div className="space-y-2">
            <Label
              htmlFor="date_of_birth"
              className="text-sm font-medium text-gray-700"
            >
              Date of Birth
            </Label>
            <Input
              id="date_of_birth"
              type="date"
              {...register("date_of_birth")}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="gender"
              className="text-sm font-medium text-gray-700"
            >
              Gender
            </Label>
            <select
              id="gender"
              {...register("gender")}
              className="w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]"
            >
              <option value="">Select gender</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>

        <div className="mt-6">
          <div className="space-y-2">
            <Label
              htmlFor="residential_address"
              className="text-sm font-medium text-gray-700"
            >
              Residential Address
            </Label>
            <Input
              id="residential_address"
              type="text"
              placeholder="Enter your address"
              {...register("residential_address")}
              className="w-full"
            />
          </div>
        </div>
      </div>

      <div>
        <Button
          type="submit"
          className="bg-[#cc3a1b] hover:bg-[#cc3a1b]/80 text-white px-8 py-2 rounded-md cursor-pointer"
          disabled={isPending}
        >
          {isPending ? <LoadingBars /> : "Save"}
        </Button>
      </div>
    </form>
  );
}
