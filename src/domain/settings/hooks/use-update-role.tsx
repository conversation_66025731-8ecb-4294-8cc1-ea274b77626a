import { useMutation, useQueryClient } from "@tanstack/react-query";
import { $http } from "@/domain/auth/lib/http";
import { toast } from "sonner";
import { setCookie } from "cookies-next";

export function useUpdateRole() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { role: string }) => {
      return await $http.patch(`/auth/complete-profile`, data);
    },
    onSuccess: (data, variables) => {
      setCookie("markket_current_role", variables.role, {
        secure: true,
        sameSite: "strict",
        maxAge: 7 * 24 * 60 * 60, // 7 days
      });
      
      queryClient.invalidateQueries({ queryKey: ["current-user"] });
      
      toast.success("Role updated successfully!", {
        description: "Your role has been switched and saved.",
        duration: 4000,
      });
    },
    onError: error => {
      console.error("Role update error:", error);
      toast.error("Failed to update role", {
        description: "Please try again later.",
      });
    },
  });
}
