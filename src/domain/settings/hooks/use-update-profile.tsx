import { useMutation, useQueryClient } from "@tanstack/react-query";
import { $http } from "@/domain/auth/lib/http";
import { toast } from "sonner";

export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: USER) => {
      return await $http.patch(`/auth/complete-profile`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["current-user"] });
      toast.success("Profile updated successfully!", {
        description: "Your profile information has been updated and saved.",
        duration: 6000,
      });
    },
    onError: error => {
      console.error("Profile update error:", error);
      toast.error("Failed to update profile", {
        description: "Please try again later.",
      });
    },
  });
}
