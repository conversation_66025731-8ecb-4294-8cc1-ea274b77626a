export const mockRequests = [
  {
    id: "REQ001",
    title: "High-Performance Gaming Laptop",
    category: "Electronics",
    subcategory: "Laptops",
    description:
      "Looking for a high-performance laptop for programming and gaming. Need at least 16GB RAM, RTX 4060 or better, and good cooling system.",
    status: "active",
    priority: "high",
    budgetMin: 1200,
    budgetMax: 1800,
    currency: "USD",
    timeline: "within_week",
    location: "New York, NY",
    viewCount: 45,
    responseCount: 8,
    createdAt: "2024-01-20T10:00:00Z",
    expiresAt: "2024-02-20T10:00:00Z",
    tags: ["Gaming", "Programming", "High-Performance"],
    isUrgent: false,
  },
  {
    id: "REQ002",
    title: "Ergonomic Office Furniture Set",
    category: "Home & Garden",
    subcategory: "Furniture",
    description:
      "Need ergonomic office chairs and height-adjustable desks for a 10-person startup office. Looking for modern design and good warranty.",
    status: "active",
    priority: "medium",
    budgetMin: 5000,
    budgetMax: 8000,
    currency: "USD",
    timeline: "within_month",
    location: "San Francisco, CA",
    viewCount: 32,
    responseCount: 12,
    createdAt: "2024-01-19T14:30:00Z",
    expiresAt: "2024-02-19T14:30:00Z",
    tags: ["Bulk Order", "Office", "Ergonomic"],
    isUrgent: false,
  },
  {
    id: "REQ003",
    title: "Professional Camera Equipment",
    category: "Electronics",
    subcategory: "Cameras",
    description:
      "Urgent need for professional camera setup for wedding photography business. Need full-frame camera, 2-3 lenses, lighting equipment.",
    status: "active",
    priority: "urgent",
    budgetMin: 3000,
    budgetMax: 5000,
    currency: "USD",
    timeline: "urgent",
    location: "Los Angeles, CA",
    viewCount: 67,
    responseCount: 15,
    createdAt: "2024-01-21T09:15:00Z",
    expiresAt: "2024-01-28T09:15:00Z",
    tags: ["Professional", "Photography", "Urgent"],
    isUrgent: true,
  },
  {
    id: "REQ004",
    title: "Organic Baby Products Bundle",
    category: "Health & Beauty",
    subcategory: "Baby Care",
    description:
      "Looking for organic, chemical-free baby products including diapers, wipes, lotions, and toys for newborn.",
    status: "completed",
    priority: "medium",
    budgetMin: 200,
    budgetMax: 400,
    currency: "USD",
    timeline: "within_week",
    location: "Chicago, IL",
    viewCount: 28,
    responseCount: 6,
    createdAt: "2024-01-15T16:45:00Z",
    expiresAt: "2024-02-15T16:45:00Z",
    tags: ["Organic", "Baby", "Chemical-Free"],
    isUrgent: false,
  },
  {
    id: "REQ005",
    title: "Smart Home Automation System",
    category: "Electronics",
    subcategory: "Smart Home",
    description:
      "Want to automate entire house with smart switches, sensors, cameras, and central hub. Compatible with Google Home preferred.",
    status: "paused",
    priority: "low",
    budgetMin: 2000,
    budgetMax: 4000,
    currency: "USD",
    timeline: "flexible",
    location: "Austin, TX",
    viewCount: 19,
    responseCount: 4,
    createdAt: "2024-01-18T11:20:00Z",
    expiresAt: "2024-03-18T11:20:00Z",
    tags: ["Smart Home", "Automation", "Google Home"],
    isUrgent: false,
  },
];

export const mockResponses = [
  {
    id: "RESP001",
    sellerId: "SELLER001",
    sellerName: "TechGear Pro",
    sellerRating: 4.8,
    sellerReviews: 156,
    sellerBadges: ["Verified", "Top Seller"],
    price: 1650,
    originalPrice: 1750,
    currency: "USD",
    message:
      "I have the perfect laptop for you! ASUS ROG Strix G17 with RTX 4060, 16GB RAM, and excellent cooling. Brand new with full warranty. I can also include a gaming mouse and laptop bag at no extra cost.",
    deliveryTime: "3-5 business days",
    warranty: "2 years manufacturer + 1 year extended",
    status: "pending",
    createdAt: "2024-01-21T14:30:00Z",
    images: ["response1.jpg", "response2.jpg", "response3.jpg"],
    isCounterOffer: false,
    sellerLocation: "New York, NY",
    responseTime: "2 hours",
    completionRate: 98,
    onTimeDelivery: 95,
  },
  {
    id: "RESP002",
    sellerId: "SELLER002",
    sellerName: "Gaming Central",
    sellerRating: 4.6,
    sellerReviews: 89,
    sellerBadges: ["Verified"],
    price: 1750,
    currency: "USD",
    message:
      "MSI Katana 17 with RTX 4070, 32GB RAM upgrade included. Premium gaming experience guaranteed! Free shipping and setup service included.",
    deliveryTime: "2-3 business days",
    warranty: "3 years comprehensive",
    status: "pending",
    createdAt: "2024-01-21T16:45:00Z",
    images: ["response4.jpg"],
    isCounterOffer: false,
    sellerLocation: "California, CA",
    responseTime: "4 hours",
    completionRate: 92,
    onTimeDelivery: 88,
  },
  {
    id: "RESP003",
    sellerId: "SELLER003",
    sellerName: "ElitePC Solutions",
    sellerRating: 4.9,
    sellerReviews: 234,
    sellerBadges: ["Verified", "Premium", "Fast Shipping"],
    price: 1580,
    currency: "USD",
    message:
      "Alienware m17 R5 with RTX 4060, 16GB DDR5, 1TB SSD. Excellent condition, barely used. Can provide detailed specs and benchmarks.",
    deliveryTime: "1-2 business days",
    warranty: "1 year remaining + 2 years extended",
    status: "pending",
    createdAt: "2024-01-22T09:15:00Z",
    images: ["response5.jpg", "response6.jpg"],
    isCounterOffer: true,
    sellerLocation: "Texas, TX",
    responseTime: "30 minutes",
    completionRate: 99,
    onTimeDelivery: 97,
  },
];

// Mock data - replace with actual API call
export const mockRequest = {
  id: "REQ001",
  title: "High-Performance Gaming Laptop",
  category: "Electronics",
  subcategory: "Laptops",
  description:
    "Looking for a high-performance laptop for programming and gaming. Need at least 16GB RAM, RTX 4060 or better, and good cooling system. Will be used for software development, 3D rendering, and gaming. Prefer brands like ASUS ROG, MSI Gaming, or Alienware.",
  status: "active",
  priority: "high",
  budgetMin: 1200,
  budgetMax: 1800,
  currency: "USD",
  budgetType: "range",
  timeline: "within_week",
  deliveryDate: "2024-02-01",
  location: "New York, NY",
  deliveryLocation: {
    address: "123 Main Street, Apt 4B",
    city: "New York",
    state: "NY",
    zipCode: "10001",
    requireSignature: true,
    fragileItems: false,
    weekendDelivery: true,
    specialInstructions:
      "Ring doorbell twice, leave at front door if no answer",
  },
  deliveryPreference: "delivery",
  contactMethod: "platform",
  phoneNumber: "+****************",
  viewCount: 45,
  responseCount: 8,
  createdAt: "2024-01-20T10:00:00Z",
  expiresAt: "2024-02-20T10:00:00Z",
  tags: ["Gaming", "Programming", "High-Performance", "RTX 4060", "16GB RAM"],
  specifications: [
    { key: "RAM", value: "Minimum 16GB DDR4/DDR5" },
    { key: "Graphics Card", value: "RTX 4060 or better" },
    { key: "Processor", value: "Intel i7 or AMD Ryzen 7" },
    { key: "Storage", value: "1TB SSD preferred" },
    { key: "Display", value: "15-17 inch, 144Hz or higher" },
    { key: "Cooling", value: "Advanced cooling system required" },
  ],
  preferredBrands: ["ASUS ROG", "MSI Gaming", "Alienware", "Razer"],
  qualityRequirements:
    "Brand new condition only. Must come with manufacturer warranty. No refurbished or open-box items.",
  referenceLinks: [
    "https://www.asus.com/laptops/for-gaming/rog-strix/",
    "https://www.msi.com/Laptops/Gaming",
  ],
  images: ["laptop1.jpg", "laptop2.jpg"],
  documents: ["specs.pdf"],
  isPrivate: false,
  allowCounterOffers: true,
  autoAcceptBestOffer: false,
  isUrgent: false,
};

export const SUBCATEGORIES: Record<string, string[]> = {
  Electronics: [
    "Smartphones",
    "Laptops",
    "Tablets",
    "Gaming",
    "Audio",
    "Cameras",
    "Smart Home",
  ],
  "Fashion & Clothing": [
    "Men's Clothing",
    "Women's Clothing",
    "Shoes",
    "Accessories",
    "Jewelry",
    "Bags",
  ],
  "Home & Garden": [
    "Furniture",
    "Decor",
    "Kitchen",
    "Bedding",
    "Garden Tools",
    "Lighting",
  ],
  "Sports & Outdoors": [
    "Fitness Equipment",
    "Outdoor Gear",
    "Sports Apparel",
    "Team Sports",
    "Water Sports",
  ],
  Automotive: [
    "Car Parts",
    "Accessories",
    "Tools",
    "Maintenance",
    "Electronics",
  ],
  "Health & Beauty": [
    "Skincare",
    "Makeup",
    "Hair Care",
    "Supplements",
    "Medical Supplies",
  ],
};

export const POPULAR_BRANDS: Record<string, string[]> = {
  Electronics: [
    "Apple",
    "Samsung",
    "Sony",
    "LG",
    "Dell",
    "HP",
    "Lenovo",
    "ASUS",
  ],
  "Fashion & Clothing": [
    "Nike",
    "Adidas",
    "Zara",
    "H&M",
    "Uniqlo",
    "Levi's",
    "Calvin Klein",
  ],
  Automotive: [
    "Toyota",
    "Honda",
    "Ford",
    "BMW",
    "Mercedes",
    "Audi",
    "Volkswagen",
  ],
  "Home & Garden": [
    "IKEA",
    "Home Depot",
    "Wayfair",
    "West Elm",
    "Pottery Barn",
  ],
};
