export interface RequestFormData {
  // Basic Information
  title: string;
  category: string;
  subcategory?: string;
  description: string;

  // Detailed Requirements
  specifications: {
    key: string;
    value: string;
  }[];
  quantity: number;
  preferredBrands?: string[];
  qualityRequirements?: string;

  // Budget & Timeline
  budgetType: "fixed" | "range" | "negotiable";
  budgetMin?: number;
  budgetMax?: number;
  currency: string;
  timeline: "urgent" | "within_week" | "within_month" | "flexible";
  deliveryDate?: string;

  // Location & Delivery
  deliveryLocation: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
    requireSignature?: boolean;
    fragileItems?: boolean;
    weekendDelivery?: boolean;
    specialInstructions?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  deliveryPreference: "pickup" | "delivery" | "both";

  // Attachments & Media
  images?: File[];
  documents?: File[];
  referenceLinks?: string[];

  // Additional Options
  isPrivate: boolean;
  allowCounterOffers: boolean;
  autoAcceptBestOffer: boolean;
  tags: string[];

  // Contact Preferences
  contactMethod: "platform" | "phone" | "email" | "all";
  phoneNumber?: string;
  preferredContactTime?: string;
}

export interface BuyerRequest {
  id: string;
  buyerId: string;
  buyerName: string;
  buyerAvatar?: string;

  // Request Details
  title: string;
  category: string;
  subcategory?: string;
  description: string;
  specifications: {
    key: string;
    value: string;
  }[];

  // Budget & Timeline
  budgetType: "fixed" | "range" | "negotiable";
  budgetMin?: number;
  budgetMax?: number;
  currency: string;
  timeline: "urgent" | "within_week" | "within_month" | "flexible";
  deliveryDate?: string;

  // Status & Metrics
  status: "draft" | "active" | "paused" | "completed" | "cancelled" | "expired";
  priority: "low" | "medium" | "high" | "urgent";
  viewCount: number;
  responseCount: number;

  // Timestamps
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;

  // Location
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };

  // Media
  images: string[];
  documents: string[];

  // Settings
  isPrivate: boolean;
  allowCounterOffers: boolean;
  autoAcceptBestOffer: boolean;
  tags: string[];

  // Responses
  responses?: SellerResponse[];
}

export interface SellerResponse {
  id: string;
  sellerId: string;
  sellerName: string;
  sellerAvatar?: string;
  sellerRating: number;
  sellerVerified: boolean;

  // Response Details
  message: string;
  proposedPrice: number;
  currency: string;
  deliveryTime: string;

  // Status
  status: "pending" | "accepted" | "rejected" | "countered";

  // Timestamps
  createdAt: string;
  updatedAt: string;

  // Additional Info
  attachments?: string[];
  samples?: string[];
  warranty?: string;
  returnPolicy?: string;
}

export interface RequestTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  icon: string;
  isPopular: boolean;
  template: Partial<RequestFormData>;
}

export interface RequestStats {
  totalRequests: number;
  activeRequests: number;
  completedRequests: number;
  totalResponses: number;
  averageResponseTime: number;
  successRate: number;
}

export const REQUEST_CATEGORIES = [
  "Electronics",
  "Fashion & Clothing",
  "Home & Garden",
  "Sports & Outdoors",
  "Books & Media",
  "Automotive",
  "Health & Beauty",
  "Toys & Games",
  "Food & Beverages",
  "Services",
  "Other",
] as const;

export const BUDGET_RANGES = [
  { label: "Under $50", min: 0, max: 50 },
  { label: "$50 - $100", min: 50, max: 100 },
  { label: "$100 - $250", min: 100, max: 250 },
  { label: "$250 - $500", min: 250, max: 500 },
  { label: "$500 - $1,000", min: 500, max: 1000 },
  { label: "$1,000 - $2,500", min: 1000, max: 2500 },
  { label: "$2,500+", min: 2500, max: null },
] as const;

export const TIMELINE_OPTIONS = [
  { value: "urgent", label: "Urgent (Within 24 hours)", icon: "🚨" },
  { value: "within_week", label: "Within a week", icon: "📅" },
  { value: "within_month", label: "Within a month", icon: "🗓️" },
  { value: "flexible", label: "Flexible timeline", icon: "⏰" },
] as const;
