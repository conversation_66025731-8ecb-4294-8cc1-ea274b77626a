"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RequestFormData } from "../types";
import { Plus, ArrowRight, ArrowLeft, Trash2, Info } from "lucide-react";
import { POPULAR_BRANDS } from "../data";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const requirementsSchema = z.object({
  quantity: z.number().min(1, "Quantity must be at least 1"),
  specifications: z
    .array(
      z.object({
        key: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  preferredBrands: z.array(z.string()).optional(),
  qualityRequirements: z.string().optional(),
});

interface RequirementsStepProps {
  data: Partial<RequestFormData>;
  onUpdate: (data: Partial<RequestFormData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export default function RequirementsStep({
  data,
  onUpdate,
  onNext,
  onPrev,
}: RequirementsStepProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<z.infer<typeof requirementsSchema>>({
    resolver: zodResolver(requirementsSchema),
    defaultValues: {
      quantity: data.quantity || 1,
      specifications: data.specifications || [],
      preferredBrands: data.preferredBrands || [],
      qualityRequirements: data.qualityRequirements || "",
    },
  });

  const onSubmit = (formData: z.infer<typeof requirementsSchema>) => {
    onUpdate(formData);
    onNext();
  };

  const addSpecification = () => {
    const currentSpecs = watch("specifications") || [];
    setValue("specifications", [...currentSpecs, { key: "", value: "" }]);
  };

  const updateSpecification = (
    index: number,
    field: "key" | "value",
    value: string
  ) => {
    const currentSpecs = watch("specifications") || [];
    const updatedSpecs = currentSpecs.map((spec, i) =>
      i === index ? { ...spec, [field]: value } : spec
    );
    setValue("specifications", updatedSpecs);
  };

  const removeSpecification = (index: number) => {
    const currentSpecs = watch("specifications") || [];
    const updatedSpecs = currentSpecs.filter((_, i) => i !== index);
    setValue("specifications", updatedSpecs);
  };

  const toggleBrand = (brand: string) => {
    const currentBrands = watch("preferredBrands") || [];
    const updatedBrands = currentBrands.includes(brand)
      ? currentBrands.filter(b => b !== brand)
      : [...currentBrands, brand];
    setValue("preferredBrands", updatedBrands);
  };

  const availableBrands = data.category
    ? POPULAR_BRANDS[data.category] || []
    : [];

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 max-w-2xl">
      <div className="grid gap-6">
        {/* Quantity */}
        <Card>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity Needed *</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  placeholder="1"
                  {...register("quantity", { valueAsNumber: true })}
                  className={errors.quantity ? "border-destructive" : ""}
                />
                {errors.quantity && (
                  <p className="text-sm text-destructive">
                    {errors.quantity.message}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Specifications */}
        <Card>
          <CardContent className="space-y-4">
            <p className="text-sm text-black font-semibold flex items-center gap-1">
              Technical Specifications (Optional)
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-4 h-4 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-muted-foreground text-white font-normal">
                      Add specific requirements like size, color, model,
                      features, etc.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </p>

            <div className="space-y-3">
              {watch("specifications")?.map((spec, index) => (
                <div key={index} className="flex gap-2 items-start">
                  <div className="flex-1 grid grid-cols-2 gap-2">
                    <Input
                      placeholder="Specification (e.g., Color)"
                      value={spec.key}
                      onChange={e =>
                        updateSpecification(index, "key", e.target.value)
                      }
                    />
                    <Input
                      placeholder="Value (e.g., Black)"
                      value={spec.value}
                      onChange={e =>
                        updateSpecification(index, "value", e.target.value)
                      }
                    />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeSpecification(index)}
                    className="shrink-0"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addSpecification}
                className="w-full gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Specification
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Preferred Brands */}
        {availableBrands.length > 0 && (
          <Card>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-black font-semibold flex items-center gap-1">
                  Preferred Brands (Optional)
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-muted-foreground text-white font-normal">
                          Select brands you prefer or trust
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </p>
                <div className="flex flex-wrap gap-2">
                  {availableBrands.map(brand => (
                    <Badge
                      key={brand}
                      variant={
                        watch("preferredBrands")?.includes(brand)
                          ? "default"
                          : "outline"
                      }
                      className="cursor-pointer hover:bg-primary/10"
                      onClick={() => toggleBrand(brand)}
                    >
                      {brand}
                    </Badge>
                  ))}
                </div>
                {(watch("preferredBrands") ?? []).length > 0 && (
                  <div className="pt-2">
                    <p className="text-sm font-medium">Selected brands:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {watch("preferredBrands")?.map(brand => (
                        <Badge
                          key={brand}
                          variant="secondary"
                          className="text-xs"
                        >
                          {brand}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quality Requirements */}
        <Card>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="quality" className="gap-1">
                Quality Requirements (Optional)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-muted-foreground text-white font-normal">
                        Specify quality standards and condition expectations
                        etc.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Textarea
                id="quality"
                placeholder="Specify quality standards, condition requirements, certifications needed, warranty expectations, etc."
                {...register("qualityRequirements")}
                className="min-h-24"
              />
              <p className="text-xs text-muted-foreground">
                {`Examples: "Brand new only", "Certified refurbished acceptable", "Must have 1-year warranty"`}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <Button
          type="button"
          variant="outline"
          onClick={onPrev}
          className="gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <Button type="submit" className="gap-2">
          Continue to Budget
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </form>
  );
}
