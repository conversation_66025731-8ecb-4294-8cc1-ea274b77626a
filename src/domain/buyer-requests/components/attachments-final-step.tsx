"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RequestFormData } from "../types";
import {
  Upload,
  Image,
  FileText,
  Link,
  ArrowLeft,
  X,
  Eye,
  Phone,
  Mail,
  MessageSquare,
  CheckCircle,
  Rocket,
} from "lucide-react";

interface AttachmentsFinalStepProps {
  data: Partial<RequestFormData>;
  onUpdate: (data: Partial<RequestFormData>) => void;
  onSubmit: () => void;
  onPrev: () => void;
  isSubmitting?: boolean;
}

export default function AttachmentsFinalStep({
  data,
  onUpdate,
  onSubmit,
  onPrev,
  isSubmitting = false,
}: AttachmentsFinalStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [newReferenceLink, setNewReferenceLink] = useState("");
  const imageInputRef = useRef<HTMLInputElement>(null);
  const documentInputRef = useRef<HTMLInputElement>(null);

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!data.contactMethod) {
      newErrors.contactMethod = "Please select a contact method";
    }

    if (data.contactMethod === "phone" && !data.phoneNumber?.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validate()) {
      onSubmit();
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const currentImages = data.images || [];
    onUpdate({ images: [...currentImages, ...files] });
  };

  const handleDocumentUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const currentDocs = data.documents || [];
    onUpdate({ documents: [...currentDocs, ...files] });
  };

  const removeImage = (index: number) => {
    const currentImages = data.images || [];
    const updatedImages = currentImages.filter((_, i) => i !== index);
    onUpdate({ images: updatedImages });
  };

  const removeDocument = (index: number) => {
    const currentDocs = data.documents || [];
    const updatedDocs = currentDocs.filter((_, i) => i !== index);
    onUpdate({ documents: updatedDocs });
  };

  const addReferenceLink = () => {
    if (newReferenceLink.trim()) {
      const currentLinks = data.referenceLinks || [];
      onUpdate({ referenceLinks: [...currentLinks, newReferenceLink.trim()] });
      setNewReferenceLink("");
    }
  };

  const removeReferenceLink = (index: number) => {
    const currentLinks = data.referenceLinks || [];
    const updatedLinks = currentLinks.filter((_, i) => i !== index);
    onUpdate({ referenceLinks: updatedLinks });
  };

  return (
    <div className="space-y-6 max-w-2xl min-w-2xl">
      <div className="grid gap-6">
        {/* Images */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Image className="w-5 h-5 text-primary" />
              Images (Optional)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              {`Add photos to show exactly what you're looking for`}
            </p>

            <div className="space-y-4">
              <Button
                variant="outline"
                onClick={() => imageInputRef.current?.click()}
                className="w-full gap-2"
              >
                <Upload className="w-4 h-4" />
                Upload Images
              </Button>

              <input
                ref={imageInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />

              {data.images && data.images.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {data.images.map((image, index) => (
                    <div key={index} className="relative group">
                      <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                        <Image className="w-8 h-8 text-muted-foreground" />
                      </div>
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                        <Button
                          size="icon"
                          variant="secondary"
                          className="h-8 w-8"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="destructive"
                          className="h-8 w-8"
                          onClick={() => removeImage(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-center mt-1 truncate">
                        {image.name}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Documents */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <FileText className="w-5 h-5 text-primary" />
              Documents (Optional)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Upload specifications, requirements, or reference documents
            </p>

            <div className="space-y-4">
              <Button
                variant="outline"
                onClick={() => documentInputRef.current?.click()}
                className="w-full gap-2"
              >
                <Upload className="w-4 h-4" />
                Upload Documents
              </Button>

              <input
                ref={documentInputRef}
                type="file"
                accept=".pdf,.doc,.docx,.txt,.xlsx,.xls"
                multiple
                onChange={handleDocumentUpload}
                className="hidden"
              />

              {data.documents && data.documents.length > 0 && (
                <div className="space-y-2">
                  {data.documents.map((doc, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm truncate">{doc.name}</span>
                      </div>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8"
                        onClick={() => removeDocument(index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Reference Links */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Link className="w-5 h-5 text-primary" />
              Reference Links (Optional)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Add links to products you like or want to reference
            </p>

            <div className="flex gap-2">
              <Input
                placeholder="https://example.com/product"
                value={newReferenceLink}
                onChange={e => setNewReferenceLink(e.target.value)}
                onKeyPress={e => e.key === "Enter" && addReferenceLink()}
              />
              <Button onClick={addReferenceLink} variant="outline">
                Add
              </Button>
            </div>

            {data.referenceLinks && data.referenceLinks.length > 0 && (
              <div className="space-y-2">
                {data.referenceLinks.map((link, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-2">
                      <Link className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm truncate">{link}</span>
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8"
                      onClick={() => removeReferenceLink(index)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contact Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Contact Preferences</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>How should sellers contact you? *</Label>
              <Select
                value={data.contactMethod || ""}
                onValueChange={value =>
                  onUpdate({
                    contactMethod: value as
                      | "platform"
                      | "phone"
                      | "email"
                      | "all",
                  })
                }
              >
                <SelectTrigger
                  className={errors.contactMethod ? "border-destructive" : ""}
                >
                  <SelectValue placeholder="Select contact method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="platform">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="w-4 h-4" />
                      Platform messaging only
                    </div>
                  </SelectItem>
                  <SelectItem value="phone">
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Phone calls preferred
                    </div>
                  </SelectItem>
                  <SelectItem value="email">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      Email preferred
                    </div>
                  </SelectItem>
                  <SelectItem value="all">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      Any method is fine
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {errors.contactMethod && (
                <p className="text-sm text-destructive">
                  {errors.contactMethod}
                </p>
              )}
            </div>

            {data.contactMethod === "phone" && (
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="+****************"
                  value={data.phoneNumber || ""}
                  onChange={e => onUpdate({ phoneNumber: e.target.value })}
                  className={errors.phoneNumber ? "border-destructive" : ""}
                />
                {errors.phoneNumber && (
                  <p className="text-sm text-destructive">
                    {errors.phoneNumber}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Privacy & Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Privacy & Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="private"
                  checked={data.isPrivate || false}
                  onCheckedChange={checked =>
                    onUpdate({ isPrivate: checked as boolean })
                  }
                />
                <Label htmlFor="private" className="text-sm">
                  Make this request private (only invited sellers can respond)
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="counterOffers"
                  checked={data.allowCounterOffers !== false}
                  onCheckedChange={checked =>
                    onUpdate({ allowCounterOffers: checked as boolean })
                  }
                />
                <Label htmlFor="counterOffers" className="text-sm">
                  Allow sellers to make counter-offers
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoAccept"
                  checked={data.autoAcceptBestOffer || false}
                  onCheckedChange={checked =>
                    onUpdate({ autoAcceptBestOffer: checked as boolean })
                  }
                />
                <Label htmlFor="autoAccept" className="text-sm">
                  Automatically accept the best offer within my budget
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={onPrev} className="gap-2">
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <Button
          onClick={handleSubmit}
          className="gap-2"
          disabled={isSubmitting}
          size="lg"
        >
          {isSubmitting ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Creating Request...
            </>
          ) : (
            <>
              <Rocket className="w-4 h-4" />
              Create Request
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
