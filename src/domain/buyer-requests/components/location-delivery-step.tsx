"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { RequestFormData } from "../types";
import {
  Truck,
  ArrowRight,
  ArrowLeft,
  Home,
  Package,
  Clock,
} from "lucide-react";

interface LocationDeliveryStepProps {
  data: Partial<RequestFormData>;
  onUpdate: (data: Partial<RequestFormData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export default function LocationDeliveryStep({
  data,
  onUpdate,
  onNext,
  onPrev,
}: LocationDeliveryStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!data.deliveryLocation?.address?.trim()) {
      newErrors.address = "Address is required";
    }

    if (!data.deliveryLocation?.city?.trim()) {
      newErrors.city = "City is required";
    }

    if (!data.deliveryLocation?.state?.trim()) {
      newErrors.state = "State/Province is required";
    }

    if (!data.deliveryLocation?.zipCode?.trim()) {
      newErrors.zipCode = "ZIP/Postal code is required";
    }

    if (!data.deliveryPreference) {
      newErrors.deliveryPreference = "Please select a delivery preference";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validate()) {
      onNext();
    }
  };

  const updateLocation = (field: string, value: boolean | string) => {
    onUpdate({
      deliveryLocation: {
        ...data.deliveryLocation,
        address: data.deliveryLocation?.address || "",
        city: data.deliveryLocation?.city || "",
        state: data.deliveryLocation?.state || "",
        zipCode: data.deliveryLocation?.zipCode || "",
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-6 max-w-2xl min-w-2xl">
      <div className="grid gap-6">
        {/* Delivery Address */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Home className="w-5 h-5 text-primary" />
              Delivery Address
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="address">Street Address *</Label>
              <Input
                id="address"
                placeholder="123 Main Street, Apt 4B"
                value={data.deliveryLocation?.address || ""}
                onChange={e => updateLocation("address", e.target.value)}
                className={errors.address ? "border-destructive" : ""}
              />
              {errors.address && (
                <p className="text-sm text-destructive">{errors.address}</p>
              )}
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  placeholder="New York"
                  value={data.deliveryLocation?.city || ""}
                  onChange={e => updateLocation("city", e.target.value)}
                  className={errors.city ? "border-destructive" : ""}
                />
                {errors.city && (
                  <p className="text-sm text-destructive">{errors.city}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">State/Province *</Label>
                <Input
                  id="state"
                  placeholder="NY"
                  value={data.deliveryLocation?.state || ""}
                  onChange={e => updateLocation("state", e.target.value)}
                  className={errors.state ? "border-destructive" : ""}
                />
                {errors.state && (
                  <p className="text-sm text-destructive">{errors.state}</p>
                )}
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="zipCode">ZIP/Postal Code *</Label>
                <Input
                  id="zipCode"
                  placeholder="10001"
                  value={data.deliveryLocation?.zipCode || ""}
                  onChange={e => updateLocation("zipCode", e.target.value)}
                  className={errors.zipCode ? "border-destructive" : ""}
                />
                {errors.zipCode && (
                  <p className="text-sm text-destructive">{errors.zipCode}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delivery Preference */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Truck className="w-5 h-5 text-primary" />
              Delivery Preference
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <RadioGroup
                value={data.deliveryPreference || ""}
                onValueChange={value =>
                  onUpdate({
                    deliveryPreference: value as "pickup" | "delivery" | "both",
                  })
                }
                className="space-y-3"
              >
                <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-accent/50">
                  <RadioGroupItem value="delivery" id="delivery" />
                  <div className="flex-1">
                    <Label
                      htmlFor="delivery"
                      className="font-medium cursor-pointer flex items-center gap-2"
                    >
                      <Truck className="w-4 h-4" />
                      Delivery Only
                    </Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      I need the items delivered to my address
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-accent/50">
                  <RadioGroupItem value="pickup" id="pickup" />
                  <div className="flex-1">
                    <Label
                      htmlFor="pickup"
                      className="font-medium cursor-pointer flex items-center gap-2"
                    >
                      <Package className="w-4 h-4" />
                      Pickup Only
                    </Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {`I can pick up the items from the seller's location`}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-accent/50">
                  <RadioGroupItem value="both" id="both" />
                  <div className="flex-1">
                    <Label
                      htmlFor="both"
                      className="font-medium cursor-pointer flex items-center gap-2"
                    >
                      <Clock className="w-4 h-4" />
                      Either Delivery or Pickup
                    </Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {`I'm flexible - either option works for me`}
                    </p>
                  </div>
                </div>
              </RadioGroup>

              {errors.deliveryPreference && (
                <p className="text-sm text-destructive">
                  {errors.deliveryPreference}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Additional Delivery Notes */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              Delivery Instructions (Optional)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="signature"
                    checked={data.deliveryLocation?.requireSignature || false}
                    onCheckedChange={checked =>
                      updateLocation("requireSignature", checked)
                    }
                  />
                  <Label htmlFor="signature" className="text-sm">
                    Signature required for delivery
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="fragile"
                    checked={data.deliveryLocation?.fragileItems || false}
                    onCheckedChange={checked =>
                      updateLocation("fragileItems", checked)
                    }
                  />
                  <Label htmlFor="fragile" className="text-sm">
                    Items are fragile - handle with care
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="weekend"
                    checked={data.deliveryLocation?.weekendDelivery || false}
                    onCheckedChange={checked =>
                      updateLocation("weekendDelivery", checked)
                    }
                  />
                  <Label htmlFor="weekend" className="text-sm">
                    Weekend delivery acceptable
                  </Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="deliveryNotes">
                  Special Delivery Instructions
                </Label>
                <Input
                  id="deliveryNotes"
                  placeholder="e.g., Leave at front door, Ring doorbell twice, etc."
                  value={data.deliveryLocation?.specialInstructions || ""}
                  onChange={e =>
                    updateLocation("specialInstructions", e.target.value)
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={onPrev} className="gap-2">
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <Button onClick={handleNext} className="gap-2">
          Continue to Attachments
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
