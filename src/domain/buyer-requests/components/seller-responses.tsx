"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  CheckCircle,
  Star,
  User,
  MessageSquare,
  Eye,
  ThumbsDown,
  Filter,
  SortAsc,
  Image as ImageIcon,
} from "lucide-react";
import { mockResponses } from "../data";

export default function SellerResponses() {
  // eslint-disable-next-line
  const [responses, setResponses] = useState(mockResponses);
  const [sortBy, setSortBy] = useState("newest");
  const [filterBy, setFilterBy] = useState("all");

  const sortedResponses = [...responses].sort((a, b) => {
    switch (sortBy) {
      case "price_low":
        return a.price - b.price;
      case "price_high":
        return b.price - a.price;
      case "rating":
        return b.sellerRating - a.sellerRating;
      case "delivery":
        return parseInt(a.deliveryTime) - parseInt(b.deliveryTime);
      case "newest":
      default:
        return (
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
    }
  });

  const filteredResponses = sortedResponses.filter(response => {
    if (filterBy === "all") return true;
    if (filterBy === "counter_offers") return response.isCounterOffer;
    if (filterBy === "verified")
      return response.sellerBadges.includes("Verified");
    if (filterBy === "top_rated") return response.sellerRating >= 4.7;
    return true;
  });

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const handleAcceptOffer = (responseId: string) => {
    // Handle accept offer logic
    console.log("Accepting offer:", responseId);
  };

  const handleRejectOffer = (responseId: string) => {
    // Handle reject offer logic
    console.log("Rejecting offer:", responseId);
  };

  const handleContactSeller = (sellerId: string) => {
    // Handle contact seller logic
    console.log("Contacting seller:", sellerId);
  };

  return (
    <TooltipProvider>
      <div className="container mx-auto px-4 py-6 overflow-y-auto scrollbar-hide">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <div className="flex-1">
            <h1 className="text-xl font-bold">Sellers Responses</h1>
            <p className="text-muted-foreground text-sm">
              {filteredResponses.length} response
              {filteredResponses.length !== 1 ? "s" : ""} for your request
            </p>
          </div>
        </div>

        <div className="flex gap-4 mb-8">
          <div className="flex items-center gap-2">
            <Filter className="w-3 h-3 text-muted-foreground" />
            <Select value={filterBy} onValueChange={setFilterBy}>
              <SelectTrigger className="w-35 text-sm">
                <SelectValue placeholder="Filter by" className="text-sm" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all" className="text-sm">
                  All Responses
                </SelectItem>
                <SelectItem value="verified" className="text-sm">
                  Verified Sellers
                </SelectItem>
                <SelectItem value="top_rated" className="text-sm">
                  Top Rated (4.7+)
                </SelectItem>
                <SelectItem value="counter_offers" className="text-sm">
                  Counter Offers
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <SortAsc className="w-3 h-3 text-muted-foreground" />
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-35 text-sm">
                <SelectValue placeholder="Sort by" className="text-sm" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest" className="text-sm">
                  Newest First
                </SelectItem>
                <SelectItem value="price_low" className="text-sm">
                  Low to High
                </SelectItem>
                <SelectItem value="price_high" className="text-sm">
                  High to Low
                </SelectItem>
                <SelectItem value="rating" className="text-sm">
                  Highest Rated
                </SelectItem>
                <SelectItem value="delivery" className="text-sm">
                  Fastest Delivery
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Responses List */}
        <div className="space-y-6">
          {filteredResponses.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <MessageSquare className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">
                  No responses found
                </h3>
                <p className="text-muted-foreground">
                  Try adjusting your filters to see more responses
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredResponses.map(response => (
              <Card
                key={response.id}
                className="hover:shadow-xs hover:bg-muted/40 transition-shadow p-0"
              >
                <CardContent className="p-3">
                  <div className="space-y-4">
                    {/* Seller Header */}
                    <div className="flex items-start justify-between w-full mb-0">
                      <div className="flex items-start justify-between gap-2 mb-1">
                        <div className="w-12 h-12 min-w-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-primary" />
                        </div>
                        <div className="flex flex-col">
                          <h3 className="text-lg font-semibold">
                            {response.sellerName}
                          </h3>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                            <div className="flex items-center gap-1">
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium">
                                {response.sellerRating}
                              </span>
                              <span>({response.sellerReviews} reviews)</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        {response.isCounterOffer && (
                          <Badge variant="outline" className="text-xs">
                            Counter Offer
                          </Badge>
                        )}
                        <div className="text-xs text-muted-foreground mt-1">
                          {formatTimeAgo(response.createdAt)}
                        </div>
                      </div>
                    </div>

                    <div className="bg-muted/30 rounded-lg p-0 mb-0">
                      <p className="text-sm leading-relaxed text-muted-foreground">
                        {response.message}
                      </p>
                    </div>

                    <div className="text-right mb-0">
                      <div className="text-2xl font-bold text-primary">
                        {response.currency} {response.price.toLocaleString()}
                      </div>
                      {response.originalPrice &&
                        response.originalPrice > response.price && (
                          <div className="text-sm text-muted-foreground line-through">
                            {response.currency}{" "}
                            {response.originalPrice.toLocaleString()}
                          </div>
                        )}
                    </div>
                    {/*         
                              <span>{response.sellerLocation}</span>
                              <span>•</span>
                              <span>Responds in {response.responseTime}</span>
                  
                  <div className="flex flex-wrap gap-1">
                    {response.sellerBadges.map(badge => (
                      <Badge key={badge} variant="secondary" className="text-xs">
                        {badge === "Verified" && <CheckCircle className="w-3 h-3 mr-1" />}
                        {badge === "Top Seller" && <Award className="w-3 h-3 mr-1" />}
                        {badge === "Premium" && <Star className="w-3 h-3 mr-1" />}
                        {badge === "Fast Shipping" && <TrendingUp className="w-3 h-3 mr-1" />}
                        {badge}
                      </Badge>
                    ))}
                  </div> */}

                    {/* Response Message */}

                    {/* Response Details */}
                    {/* <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Delivery Time</p>
                        <p className="text-muted-foreground">{response.deliveryTime}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Warranty</p>
                        <p className="text-muted-foreground">{response.warranty}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Completion Rate</p>
                        <p className="text-muted-foreground">{response.completionRate}%</p>
                      </div>
                    </div>
                  </div> */}

                    {/* Images */}
                    {response.images.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <ImageIcon className="w-4 h-4" />
                          Images ({response.images.length})
                        </h4>
                        <div className="flex gap-2">
                          {response.images.slice(0, 4).map((image, index) => (
                            <div
                              key={index}
                              className="w-20 h-20 bg-muted rounded-lg flex items-center justify-center"
                            >
                              <ImageIcon className="w-6 h-6 text-muted-foreground" />
                            </div>
                          ))}
                          {response.images.length > 4 && (
                            <div className="w-20 h-20 bg-muted rounded-lg flex items-center justify-center">
                              <span className="text-xs text-muted-foreground">
                                +{response.images.length - 4}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2 pt-2 border-t">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            onClick={() => handleAcceptOffer(response.id)}
                            className="gap-2 hover:bg-primary/90 transition-colors duration-200 cursor-pointer !px-2"
                          >
                            <CheckCircle className="!w-[14px] !h-[14px]" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          className="bg-black text-white border shadow-md"
                        >
                          Accept Offer
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            onClick={() =>
                              handleContactSeller(response.sellerId)
                            }
                            className="gap-2 hover:bg-accent hover:text-accent-foreground transition-colors duration-200 cursor-pointer !px-2"
                          >
                            <MessageSquare className="!w-[14px] !h-[14px]" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          className="bg-black text-white border shadow-md"
                        >
                          Contact Seller
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            className="gap-2 hover:bg-accent hover:text-accent-foreground transition-colors duration-200 cursor-pointer !px-2"
                          >
                            <Eye className="!w-[14px] !h-[14px]" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          className="bg-black text-white border shadow-md"
                        >
                          View Details
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            className="gap-2 border border-red-300 hover:bg-red-50 hover:text-red-600 transition-colors duration-200 cursor-pointer !px-2"
                            onClick={() => handleRejectOffer(response.id)}
                          >
                            <ThumbsDown className="!w-[14px] !h-[14px] text-red-500" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          className="bg-black text-white border shadow-md"
                        >
                          Reject Offer
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </TooltipProvider>
  );
}
