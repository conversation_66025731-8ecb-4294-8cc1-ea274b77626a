"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RequestFormData, REQUEST_CATEGORIES } from "../types";
import { <PERSON>rkles, ArrowRight, Tag } from "lucide-react";
import { SUBCATEGORIES } from "../data";

const formSchema = z.object({
  title: z.string().min(10, "Title should be at least 10 characters"),
  category: z.string().min(1, "Please select a category"),
  subcategory: z.string().optional(),
  description: z
    .string()
    .min(50, "Description should be at least 50 characters"),
  tags: z.array(z.string()).optional(),
});

interface BasicInfoStepProps {
  data: Partial<RequestFormData>;
  onUpdate: (data: Partial<RequestFormData>) => void;
  onNext: () => void;
}

const POPULAR_TAGS = [
  "New",
  "Used",
  "Refurbished",
  "Bulk Order",
  "Wholesale",
  "Premium Quality",
  "Budget Friendly",
  "Eco-Friendly",
  "Handmade",
  "Custom",
];

export default function BasicInfoStep({
  data,
  onUpdate,
  onNext,
}: BasicInfoStepProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: data.title || "",
      category: data.category || "",
      subcategory: data.subcategory || "",
      description: data.description || "",
      tags: data.tags || [],
    },
  });

  const handleNext = form.handleSubmit(formData => {
    onUpdate(formData);
    onNext();
  });

  const handleTagToggle = (tag: string) => {
    const currentTags = form.getValues("tags") || [];
    const updatedTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];

    form.setValue("tags", updatedTags);
    onUpdate({ tags: updatedTags });
  };

  const subcategories = form.watch("category")
    ? SUBCATEGORIES[form.watch("category")] || []
    : [];

  return (
    <form onSubmit={handleNext} className="space-y-6 max-w-2xl">
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Sparkles className="w-5 h-5 text-primary" />
              Request Title
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">What do you need? *</Label>
              <Input
                id="title"
                placeholder="e.g., High-performance gaming laptop for video editing"
                {...form.register("title")}
                className={
                  form.formState.errors.title ? "border-destructive" : ""
                }
              />
              {form.formState.errors.title && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.title.message}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                {form.watch("title")?.length || 0}/100 characters
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Category & Type</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={form.watch("category")}
                  onValueChange={value => {
                    form.setValue("category", value);
                    form.setValue("subcategory", "");
                    onUpdate({ category: value, subcategory: "" });
                  }}
                >
                  <SelectTrigger
                    className={
                      form.formState.errors.category &&
                      form.watch("category") === ""
                        ? "border-destructive"
                        : ""
                    }
                  >
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {REQUEST_CATEGORIES.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.category &&
                  form.watch("category") === "" && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.category.message}
                    </p>
                  )}
              </div>

              {subcategories.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="subcategory">Subcategory</Label>
                  <Select
                    value={form.watch("subcategory")}
                    onValueChange={value => {
                      form.setValue("subcategory", value);
                      onUpdate({ subcategory: value });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select subcategory" />
                    </SelectTrigger>
                    <SelectContent>
                      {subcategories.map(subcategory => (
                        <SelectItem key={subcategory} value={subcategory}>
                          {subcategory}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Detailed Description</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="description">{`Describe what you're looking for *`}</Label>
              <Textarea
                id="description"
                placeholder="Provide detailed information about your requirements, intended use, preferred features, etc."
                {...form.register("description")}
                className={`min-h-32 ${
                  form.formState.errors.description ? "border-destructive" : ""
                }`}
              />
              {form.formState.errors.description && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.description.message}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                {form.watch("description")?.length || 0}/1000 characters
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Tag className="w-5 h-5 text-primary" />
              Tags (Optional)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground">
                Add tags to help sellers find your request
              </p>
              <div className="flex flex-wrap gap-2">
                {POPULAR_TAGS.map(tag => (
                  <Badge
                    key={tag}
                    variant={
                      form.watch("tags")?.includes(tag) ? "default" : "outline"
                    }
                    className={`cursor-pointer transition-colors ${
                      form.watch("tags")?.includes(tag)
                        ? "hover:bg-primary/80"
                        : "hover:bg-primary/10"
                    }`}
                    onClick={() => handleTagToggle(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
              {(form.watch("tags") ?? []).length > 0 && (
                <div className="pt-2">
                  <p className="text-sm font-medium">Selected tags:</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {(form.watch("tags") ?? []).map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end pt-6">
        <Button type="submit" size="lg" className="gap-2 cursor-pointer">
          Continue
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </form>
  );
}
