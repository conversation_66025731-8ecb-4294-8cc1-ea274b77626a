import React from "react";
import { Package, ChevronRight, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export const WelcomeScreen: React.FC<{ onNext: () => void }> = ({ onNext }) => {
  return (
    <div className="text-center py-16">
      <div className="max-w-2xl mx-auto">
        <Package className="w-16 h-16 mx-auto text-primary mb-8" />
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Welcome to Product Request
        </h2>
        <p className="text-sm text-gray-600 mb-8">
          Can&apos;t find what you&apos;re looking for? Let us help you get
          exactly what you need. Create a request and receive offers from
          multiple sellers.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white p-6 rounded-md border border-gray-100 flex items-center justify-center flex-col">
            <Package className="w-8 h-8 text-primary mb-4" />
            <h3 className="font-medium mb-2">Describe Your Need</h3>
            <p className="text-sm text-gray-500">
              Tell us exactly what you&apos;re looking for
            </p>
          </div>
          <div className="bg-white p-6 rounded-md border border-gray-100 flex items-center justify-center flex-col">
            <Package className="w-8 h-8 text-primary mb-4" />
            <h3 className="font-medium mb-2">Get Multiple Offers</h3>
            <p className="text-sm text-gray-500">
              Receive quotes from verified sellers
            </p>
          </div>
          <div className="bg-white p-6 rounded-md border border-gray-100 flex items-center justify-center flex-col">
            <Package className="w-8 h-8 text-primary mb-4" />
            <h3 className="font-medium mb-2">Choose Best Deal</h3>
            <p className="text-sm text-gray-500">
              Select the offer that suits you best
            </p>
          </div>
        </div>
        <Button onClick={onNext} className="w-full md:w-auto cursor-pointer">
          Start Your Request <ChevronRight className="w-4 h-4 ml-2" />
        </Button>

        <div className="max-w-2xl mx-auto mt-8">
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div>
                  <h3 className="font-semibold text-lg mb-2 flex items-center gap-2">
                    {" "}
                    <Sparkles className="w-5 h-5 text-primary" /> Pro Tips for
                    Better Responses
                  </h3>
                  <ul className="space-y-1 flex flex-col  items-start text-sm text-muted-foreground">
                    <li>
                      • Be specific about your requirements to get accurate
                      quotes
                    </li>
                    <li>• Include images or reference links when possible</li>
                    <li>
                      • Set a realistic budget range to attract serious sellers
                    </li>
                    <li>• Provide clear delivery instructions and timeline</li>
                    <li>
                      • Check seller ratings and reviews before accepting offers
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
