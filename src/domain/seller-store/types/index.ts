export interface StoreData {
  basicInfo: {
    storeName: string;
    description: string;
    category: string;
    businessType: string;
    tagline: string;
    logo: File | null;
    banner: File | null;
  };
  businessDetails: {
    legalName: string;
    registrationNumber: string;
    taxId: string;
    address: string;
    phone: string;
    email: string;
    documents: {
      businessLicense: File | null;
      taxCertificate: File | null;
      idVerification: File | null;
    };
  };
  policies: {
    shippingZones: string[];
    processingTime: string;
    returnWindow: string;
    returnConditions: string;
    refundTime: string;
    customPolicies: string;
  };
  categories: {
    mainCategories: string[];
    collections: string[];
  };
}
