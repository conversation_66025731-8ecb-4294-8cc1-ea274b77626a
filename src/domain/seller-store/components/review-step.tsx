import React from "react";
import { ChevronRight, ChevronLeft } from "lucide-react";
import { StoreData } from "@/domain/seller-store/types";

export const ReviewStep: React.FC<{
  data: StoreData;
  onNext: () => void;
  onPrev: () => void;
}> = ({ data, onNext, onPrev }) => {
  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Review Your Store Details
        </h2>
        <p className="text-gray-600 mb-6">
          Please review all the information below before launching your store.
        </p>

        {/* Basic Info Summary */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-800 mb-2">Basic Info</h3>
          <p>
            <strong>Store Name:</strong> {data.basicInfo.storeName}
          </p>
          <p>
            <strong>Description:</strong> {data.basicInfo.description}
          </p>
          <p>
            <strong>Category:</strong> {data.basicInfo.category}
          </p>
        </div>

        {/* Business Details Summary */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-800 mb-2">Business Details</h3>
          <p>
            <strong>Legal Name:</strong> {data.businessDetails.legalName}
          </p>
          <p>
            <strong>Address:</strong> {data.businessDetails.address}
          </p>
        </div>

        {/* Add more sections as needed */}

        <div className="flex justify-between mt-8">
          <button
            onClick={onPrev}
            className="flex items-center px-6 py-2 text-gray-600 hover:text-gray-800"
          >
            <ChevronLeft className="w-5 h-5 mr-2" />
            Previous
          </button>
          <button
            onClick={onNext}
            className="bg-black hover:bg-black/80 cursor-pointer text-white px-6 py-2 rounded-lg flex items-center"
          >
            Continue
            <ChevronRight className="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>
    </div>
  );
};
