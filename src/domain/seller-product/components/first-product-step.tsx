// const FirstProductStep: React.FC<{
//   data: StoreData["firstProduct"];
//   onUpdate: (data: Partial<StoreData["firstProduct"]>) => void;
//   onNext: () => void;
//   onPrev: () => void;
// }> = ({ data, onUpdate, onNext, onPrev }) => {
//   const [errors, setErrors] = useState<Record<string, string>>({});

//   const validate = () => {
//     const newErrors: Record<string, string> = {};
//     if (!data.name) newErrors.name = "Product name is required";
//     if (!data.description)
//       newErrors.description = "Product description is required";
//     if (!data.price || data.price <= 0)
//       newErrors.price = "Valid price is required";

//     setErrors(newErrors);
//     return Object.keys(newErrors).length === 0;
//   };

//   const handleNext = () => {
//     if (validate()) {
//       onNext();
//     }
//   };

//   return (
//     <div className="max-w-2xl mx-auto">
//       <div className="bg-white rounded-lg shadow-sm p-8">
//         <h2 className="text-2xl font-bold text-gray-900 mb-6">
//           Add Your First Product
//         </h2>
//         <p className="text-gray-600 mb-6">
//           Let's add your first product to get your store started. You can add
//           more products later.
//         </p>

//         <div className="space-y-6">
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-2">
//               Product Name *
//             </label>
//             <input
//               type="text"
//               value={data.name}
//               onChange={e => onUpdate({ name: e.target.value })}
//               className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
//                 errors.name ? "border-red-500" : "border-gray-300"
//               }`}
//               placeholder="Enter product name"
//             />
//             {errors.name && (
//               <p className="text-red-500 text-sm mt-1">{errors.name}</p>
//             )}
//           </div>

//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-2">
//               Product Description *
//             </label>
//             <textarea
//               value={data.description}
//               onChange={e => onUpdate({ description: e.target.value })}
//               rows={4}
//               className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
//                 errors.description ? "border-red-500" : "border-gray-300"
//               }`}
//               placeholder="Describe your product in detail..."
//             />
//             {errors.description && (
//               <p className="text-red-500 text-sm mt-1">{errors.description}</p>
//             )}
//           </div>

//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-2">
//               Price *
//             </label>
//             <div className="relative">
//               <span className="absolute left-3 top-2 text-gray-500">$</span>
//               <input
//                 type="number"
//                 step="0.01"
//                 value={data.price || ""}
//                 onChange={e =>
//                   onUpdate({ price: parseFloat(e.target.value) || 0 })
//                 }
//                 className={`w-full pl-8 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
//                   errors.price ? "border-red-500" : "border-gray-300"
//                 }`}
//                 placeholder="0.00"
//               />
//             </div>
//             {errors.price && (
//               <p className="text-red-500 text-sm mt-1">{errors.price}</p>
//             )}
//           </div>

//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-2">
//               Product Images
//             </label>
//             <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
//               {[1, 2, 3, 4].map(i => (
//                 <div
//                   key={i}
//                   className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center"
//                 >
//                   <Upload className="w-6 h-6 text-gray-400 mx-auto mb-1" />
//                   <p className="text-xs text-gray-600">Image {i}</p>
//                   <input type="file" accept="image/*" className="hidden" />
//                 </div>
//               ))}
//             </div>
//             <p className="text-sm text-gray-500 mt-2">
//               Add up to 4 high-quality images of your product
//             </p>
//           </div>

//           <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
//             <h4 className="font-semibold text-blue-900 mb-2">📸 Image Tips</h4>
//             <ul className="text-blue-800 text-sm space-y-1">
//               <li>• Use high-resolution images (at least 1000x1000 pixels)</li>
//               <li>• Show multiple angles and details</li>
//               <li>• Use good lighting and clean backgrounds</li>
//               <li>• Include lifestyle shots showing the product in use</li>
//             </ul>
//           </div>
//         </div>

//         <div className="flex justify-between mt-8">
//           <button
//             onClick={onPrev}
//             className="flex items-center px-6 py-2 text-gray-600 hover:text-gray-800"
//           >
//             <ChevronLeft className="w-5 h-5 mr-2" />
//             Previous
//           </button>
//           <button
//             onClick={handleNext}
//             className="bg-black hover:bg-black/80 cursor-pointer text-white px-6 py-2 rounded-lg flex items-center"
//           >
//             Continue
//             <ChevronRight className="w-5 h-5 ml-2" />
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };
