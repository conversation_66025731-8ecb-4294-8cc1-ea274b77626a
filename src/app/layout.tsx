import type { <PERSON>ada<PERSON> } from "next";
import { Jost } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import Providers from "./providers";

const jost = Jost({
  subsets: ["latin"],
  variable: "--font-jost",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Markket",
  description: "Your next-gen product showcase platform",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${jost.variable} font-jost font-sans bg-white text-gray-900`}
        data-navbar-theme="light"
      >
        <Providers>
          <div className="flex flex-col min-h-screen">
            <main className="flex-1 w-full overflow-x-hidden">{children}</main>
          </div>
        </Providers>
        <Toaster />
      </body>
    </html>
  );
}
