"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import useGetMe from "@/domain/auth/hooks/use-auth-me";
import ProfileForm from "@/domain/settings/components/profile-form";
import { Camera } from "lucide-react";
import { useRef, useState } from "react";

const getInitials = (name: string) => {
  return name
    .split(" ")
    .map(n => n[0])
    .join("")
    .toUpperCase();
};

export default function ProfilePage() {
  const [avatarUrl, setAvatarUrl] = useState<null | string>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data: user, isLoading } = useGetMe();

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const localUrl = URL.createObjectURL(file);
      setAvatarUrl(localUrl);
    }
  };

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <div>Error loading user data</div>;
  }

  return (
    <div className="">
      <div className="flex items-center gap-4 mb-8">
        <div className="relative group">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="hidden"
          />
          <Avatar className="w-16 h-16 bg-gray-200 rounded-lg">
            {avatarUrl || (user && user.profile_image) ? (
              <AvatarImage
                src={user.profile_image ? user.profile_image : null}
                alt={user.email}
              />
            ) : (
              <AvatarFallback className="text-gray-600 text-lg font-medium rounded-lg">
                {getInitials(
                  user.first_name && user.last_name
                    ? `${user.first_name} ${user.last_name}`
                    : "Guest"
                )}
              </AvatarFallback>
            )}
          </Avatar>
          <div
            onClick={handleAvatarClick}
            className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer rounded-lg"
          >
            <Camera className="text-white size-4" />
          </div>
        </div>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 capitalize">
            {user.first_name && user.last_name
              ? `${user.first_name} ${user.last_name}`
              : "Guest"}
          </h1>
          <p className="text-gray-600 capitalize">
            {user.role === "user" ? "buyer" : user.role}
          </p>
        </div>
      </div>

      <ProfileForm initialData={user} />
    </div>
  );
}
