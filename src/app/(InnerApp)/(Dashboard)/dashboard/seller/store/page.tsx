import { But<PERSON> } from "@/components/ui/button";
import { Store, Plus } from "lucide-react";
import Link from "next/link";

export default function StorePage() {
  return (
    <div className="h-[80vh] flex flex-col items-center justify-center p-6 text-center">
      <Store className="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h1 className="text-2xl font-bold mb-2">Create Your Store</h1>
      <p className="text-gray-500 mb-6">
        You haven&apos;t created a store yet. Start selling by setting up your
        store now.
      </p>
      <Link href="/dashboard/seller/store/create">
        <Button className="flex items-center gap-2 cursor-pointer">
          <Plus className="w-4 h-4" />
          Create Store
        </Button>
      </Link>
    </div>
  );
}
