"use client";

import React, { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  DollarSign,
  Calendar,
  Eye,
  MessageSquare,
  Edit,
  Pause,
  Play,
  X,
  CheckCircle,
  AlertCircle,
  Clock,
  User,
  Star,
  Phone,
  ExternalLink,
  Download,
  Image as ImageIcon,
  FileText,
  Share2,
  MoreHorizontal,
  Users,
  Loader,
} from "lucide-react";
import { mockRequest, mockResponses } from "@/domain/buyer-requests/data";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function RequestDetailsPage() {
  const [request] = useState(mockRequest);
  const [responses] = useState(mockResponses);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800 border-red-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "low":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const daysUntilExpiry = Math.ceil(
    (new Date(request.expiresAt).getTime() - new Date().getTime()) /
      (1000 * 60 * 60 * 24)
  );

  return (
    <div className="px-4 lg:px-6 py-4">
      {/* Header */}
      <div className="flex items-start gap-4 mb-6">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h1 className="text-xl font-bold">{request.title}</h1>
            <Badge variant="outline" className="text-muted-foreground px-1.5">
              {request.status === "completed" ? (
                <CheckCircle className="fill-green-500 dark:fill-green-400" />
              ) : request.status === "paused" ? (
                <Pause className="fill-yellow-500 dark:fill-yellow-400" />
              ) : (
                <Loader className="animate-spin" />
              )}
              {request.status}
            </Badge>
          </div>
          <p className="text-muted-foreground text-sm">
            Request ID: {request.id}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="gap-2">
            <Share2 className="w-4 h-4" />
            Share
          </Button>
          <Button variant="outline" size="sm" className="gap-2">
            <Edit className="w-4 h-4" />
            Edit
          </Button>
          <Button variant="outline" size="sm">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Quick Stats */}

      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card grid grid-cols-1 gap-4  *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4 mb-8">
        <Card className="py-0 ">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <Eye className="w-6 h-6 text-gray-600 mx-auto" />
              </div>
              <div>
                <p className="text-2xl font-bold">{request.viewCount}</p>
                <p className="text-sm text-muted-foreground">Views</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="py-0">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-6 h-6 text-gray-600 mx-auto" />
              </div>
              <div>
                <p className="text-2xl font-bold">{request.responseCount}</p>
                <p className="text-sm text-muted-foreground">Responses</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="py-0">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <Calendar className="w-6 h-6 text-gray-600 mx-auto" />
              </div>
              <div>
                <p className="text-2xl font-bold">{daysUntilExpiry}</p>
                <p className="text-sm text-muted-foreground">Days Left</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="py-0">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-gray-600 mx-auto" />
              </div>
              <div>
                <p className="text-lg font-bold">
                  {request.currency} {request.budgetMin.toLocaleString()} -{" "}
                  {request.budgetMax.toLocaleString()}
                </p>
                <p className="text-sm text-muted-foreground">Budget</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Left Column - Request Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <Card>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Description</h4>
                <p className="text-muted-foreground leading-relaxed text-sm">
                  {request.description}
                </p>
              </div>

              <Separator />

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Category</h4>
                  <p className="text-muted-foreground text-sm">
                    {request.category} → {request.subcategory}
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Priority</h4>
                  <Badge className={getPriorityColor(request.priority)}>
                    {request.priority}
                  </Badge>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Tags</h4>
                <div className="flex flex-wrap gap-2">
                  {request.tags.map(tag => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Specifications */}
          {request.specifications.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Technical Specifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {request.specifications.map((spec, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-start py-2 border-b last:border-b-0"
                    >
                      <span className="font-medium text-sm">{spec.key}</span>
                      <span className="text-sm text-muted-foreground text-right max-w-xs">
                        {spec.value}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quality Requirements */}
          {request.qualityRequirements && (
            <Card>
              <CardHeader>
                <CardTitle>Quality Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed text-sm">
                  {request.qualityRequirements}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Preferred Brands */}
          {request.preferredBrands.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Preferred Brands</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {request.preferredBrands.map(brand => (
                    <Badge key={brand} variant="secondary" className="text-xs">
                      {brand}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Reference Links */}
          {request.referenceLinks.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Reference Links</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {request.referenceLinks.map((link, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 border rounded"
                    >
                      <ExternalLink className="w-4 h-4 text-muted-foreground" />
                      <a
                        href={link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline truncate"
                      >
                        {link}
                      </a>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Attachments */}
          {(request.images.length > 0 || request.documents.length > 0) && (
            <Card>
              <CardHeader>
                <CardTitle>Attachments</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {request.images.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center gap-2">
                      <ImageIcon className="w-4 h-4" />
                      Images ({request.images.length})
                    </h4>
                    <div className="flex flex-wrap gap-3">
                      {request.images.map((image, index) => (
                        <div
                          key={index}
                          className="h-20 w-20 bg-muted rounded-lg flex items-center justify-center"
                        >
                          <ImageIcon className="w-8 h-8 text-muted-foreground" />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {request.documents.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      Documents ({request.documents.length})
                    </h4>
                    <div className="space-y-2">
                      {request.documents.map((doc, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center gap-2">
                            <FileText className="w-4 h-4 text-muted-foreground" />
                            <span className="text-sm">{doc}</span>
                          </div>
                          <Button size="sm" variant="ghost">
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Request Status & Actions */}
          <Card className="gap-0">
            <CardHeader>
              <CardTitle>Request Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 items-start">
              <div className="text-startr">
                <Badge
                  variant="outline"
                  className="text-muted-foreground px-1.5"
                >
                  {request.status === "completed" ? (
                    <CheckCircle className="fill-green-500 dark:fill-green-400" />
                  ) : request.status === "paused" ? (
                    <Pause className="fill-yellow-500 dark:fill-yellow-400" />
                  ) : (
                    <Loader className="animate-spin" />
                  )}
                  {request.status}
                </Badge>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground text-sm">
                    Created:
                  </span>
                  <span className="text-sm">
                    {formatDate(request.createdAt)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground text-sm">
                    Expires:
                  </span>
                  <span
                    className={
                      daysUntilExpiry <= 7
                        ? "text-red-600 font-medium text-sm"
                        : "text-sm"
                    }
                  >
                    {formatDate(request.expiresAt)}
                  </span>
                </div>
                {daysUntilExpiry <= 7 && (
                  <div className="flex items-center text-sm gap-1 text-red-600 text-xs">
                    <AlertCircle className="w-3 h-3" />
                    <span>Expires in {daysUntilExpiry} days</span>
                  </div>
                )}
              </div>

              <Separator />

              <div className="space-x-2">
                {request.status === "active" && (
                  <>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="outline" className="w-fit gap-2">
                          <Pause className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        Temporarily pause this request
                      </TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="outline" className="w-fit gap-2">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Modify request details</TooltipContent>
                    </Tooltip>
                  </>
                )}
                {request.status === "paused" && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button className="w-full gap-2">
                        <Play className="w-4 h-4" />
                        Resume Request
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Resume this paused request</TooltipContent>
                  </Tooltip>
                )}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="destructive" className="w-fit gap-2">
                      <X className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    Permanently cancel this request
                  </TooltipContent>
                </Tooltip>
              </div>
            </CardContent>
          </Card>

          {/* Budget & Timeline */}
          <Card>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Budget</h4>
                  <p className="text-xl font-bold text-primary">
                    {request.currency} {request.budgetMin.toLocaleString()} -{" "}
                    {request.budgetMax.toLocaleString()}
                  </p>
                  <p className="text-sm text-muted-foreground capitalize">
                    {request.budgetType} budget
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Timeline</h4>
                  <p className="text-sm text-muted-foreground capitalize">
                    {request.timeline.replace("_", " ")}
                  </p>
                  {request.deliveryDate && (
                    <p className="text-sm text-muted-foreground">
                      Preferred by:{" "}
                      {new Date(request.deliveryDate).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location & Delivery */}
          <Card>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Delivery Address</h4>
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>{request.deliveryLocation.address}</p>
                  <p className="text-sm">
                    {request.deliveryLocation.city},{" "}
                    {request.deliveryLocation.state}{" "}
                    {request.deliveryLocation.zipCode}
                  </p>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Delivery Preference</h4>
                <Badge variant="outline" className="capitalize">
                  {request.deliveryPreference}
                </Badge>
              </div>

              {request.deliveryLocation.specialInstructions && (
                <div>
                  <h4 className="font-semibold mb-2">Special Instructions</h4>
                  <p className="text-sm text-muted-foreground">
                    {request.deliveryLocation.specialInstructions}
                  </p>
                </div>
              )}

              <div className="flex flex-wrap gap-2">
                {request.deliveryLocation.requireSignature && (
                  <Badge variant="outline" className="text-xs">
                    Signature Required
                  </Badge>
                )}
                {request.deliveryLocation.fragileItems && (
                  <Badge variant="outline" className="text-xs">
                    Fragile Items
                  </Badge>
                )}
                {request.deliveryLocation.weekendDelivery && (
                  <Badge variant="outline" className="text-xs">
                    Weekend OK
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <MessageSquare className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm capitalize">
                  {request.contactMethod} messaging
                </span>
              </div>
              {request.phoneNumber && (
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">{request.phoneNumber}</span>
                </div>
              )}

              <Separator />

              <div className="space-y-2 text-xs text-muted-foreground">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3" />
                  <span>
                    Counter-offers{" "}
                    {request.allowCounterOffers ? "allowed" : "not allowed"}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  {request.isPrivate ? (
                    <Eye className="w-3 h-3" />
                  ) : (
                    <Users className="w-3 h-3" />
                  )}
                  <span>
                    {request.isPrivate ? "Private request" : "Public request"}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Responses Section */}
      {responses.length > 0 && (
        <div className="mt-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">
              Seller Responses ({responses.length})
            </h2>
            <Link href={`/dashboard/buyer/requests/${request.id}/responses`}>
              <Button className="gap-2">
                <MessageSquare className="w-4 h-4" />
                View All Responses
              </Button>
            </Link>
          </div>

          <div className="grid gap-4">
            {responses.slice(0, 2).map(response => (
              <Card
                key={response.id}
                className="hover:shadow-md transition-shadow"
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex items-start gap-4 flex-1">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-primary" />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold">
                            {response.sellerName}
                          </h4>
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm">
                              {response.sellerRating}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              ({response.sellerReviews} reviews)
                            </span>
                          </div>
                        </div>

                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {response.message}
                        </p>

                        <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <DollarSign className="w-3 h-3" />
                            <span className="font-medium text-primary">
                              {response.currency}{" "}
                              {response.price.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            <span>{response.deliveryTime}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <CheckCircle className="w-3 h-3" />
                            <span>{response.warranty}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2 shrink-0">
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                      <Button size="sm">Accept Offer</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
