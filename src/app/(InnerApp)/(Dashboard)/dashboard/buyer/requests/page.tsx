"use client";

import React, { useState, useEffect, Suspense } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Package2,
  ShoppingCart,
  Plus,
  Search,
  Eye,
  MessageSquare,
  DollarSign,
  Calendar,
  MapPin,
  TrendingUp,
  CheckCircle,
  Pause,
  BarChart3,
  Users,
  Loader,
} from "lucide-react";
import { mockRequests } from "@/domain/buyer-requests/data";

import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { useIsMobile } from "@/hooks/use-mobile";
import SellerResponses from "@/domain/buyer-requests/components/seller-responses";

const RequestsContent = () => {
  const searchParams = useSearchParams();
  // eslint-disable-next-line
  const [requests, setRequests] = useState(mockRequests);
  const [filteredRequests, setFilteredRequests] = useState(mockRequests);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  const [showSuccess, setShowSuccess] = useState(false);

  const isMobile = useIsMobile();

  // Check for success parameter
  useEffect(() => {
    if (searchParams.get("success") === "true") {
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 5000);
    }
  }, [searchParams]);

  // Filter and search logic
  useEffect(() => {
    let filtered = requests;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(
        request =>
          request.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          request.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          request.tags.some(tag =>
            tag.toLowerCase().includes(searchQuery.toLowerCase())
          )
      );
    }

    // Category filter
    if (selectedCategory !== "all") {
      filtered = filtered.filter(
        request => request.category === selectedCategory
      );
    }

    // Status filter
    if (selectedStatus !== "all") {
      filtered = filtered.filter(request => request.status === selectedStatus);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "oldest":
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        case "most_responses":
          return b.responseCount - a.responseCount;
        case "budget_high":
          return (b.budgetMax || 0) - (a.budgetMax || 0);
        case "budget_low":
          return (a.budgetMin || 0) - (b.budgetMin || 0);
        default:
          return 0;
      }
    });

    setFilteredRequests(filtered);
  }, [requests, searchQuery, selectedCategory, selectedStatus, sortBy]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800 border-red-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "low":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return date.toLocaleDateString();
  };

  const categories = [...new Set(requests.map(r => r.category))];
  const activeRequests = requests.filter(r => r.status === "active").length;
  const totalResponses = requests.reduce((sum, r) => sum + r.responseCount, 0);

  return (
    <section className="w-full p-3 md:p-6">
      {/* Success Message */}
      {showSuccess && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <div>
            <p className="font-medium text-green-800">
              Request Created Successfully!
            </p>
            <p className="text-sm text-green-600">
              Your request is now live and sellers can start responding.
            </p>
          </div>
        </div>
      )}

      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card grid grid-cols-1 gap-4  *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4 mb-8">
        <Card className="py-0 ">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Requests</p>
                <p className="text-2xl font-bold">{requests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="py-0">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Active Requests</p>
                <p className="text-2xl font-bold">{activeRequests}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="py-0">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Responses</p>
                <p className="text-2xl font-bold">{totalResponses}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="py-0">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <Users className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Avg. Responses</p>
                <p className="text-2xl font-bold">
                  {requests.length > 0
                    ? Math.round(totalResponses / requests.length)
                    : 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col lg:flex-row gap-4 mb-8">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search requests by title, description, or tags..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="paused">Paused</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="most_responses">Most Responses</SelectItem>
              <SelectItem value="budget_high">Highest Budget</SelectItem>
              <SelectItem value="budget_low">Lowest Budget</SelectItem>
            </SelectContent>
          </Select>
          <Link href="/dashboard/buyer/requests/create">
            <Button
              size="lg"
              className="gap-2 shadow-lg !h-9 !px-3 cursor-pointer"
            >
              <Plus className="w-3 h-3" />
              Create New Request
            </Button>
          </Link>
        </div>
      </div>

      {/* Requests List */}
      <div className="">
        {filteredRequests.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Package2 className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No requests found</h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery ||
                selectedCategory !== "all" ||
                selectedStatus !== "all"
                  ? "Try adjusting your filters or search terms"
                  : "Create your first request to get started"}
              </p>
              {!searchQuery &&
                selectedCategory === "all" &&
                selectedStatus === "all" && (
                  <Link href="/dashboard/buyer/requests/new">
                    <Button className="gap-2">
                      <Plus className="w-4 h-4" />
                      Create Your First Request
                    </Button>
                  </Link>
                )}
            </CardContent>
          </Card>
        ) : (
          filteredRequests.map(request => (
            <Card
              key={request.id}
              className="hover:shadow-xs hover:bg-muted/50 transition-shadow py-0 border-0 border-b border-t rounded-none border-muted"
            >
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-start gap-4">
                  {/* Request Icon & Basic Info */}
                  <div className="flex items-start gap-4 flex-1">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center shrink-0">
                      {request.category === "Electronics" ? (
                        <Package2 className="w-6 h-6 text-primary" />
                      ) : (
                        <ShoppingCart className="w-6 h-6 text-primary" />
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex md:items-center flex-col md:flex-row   gap-2 mb-2">
                        <h3 className="text-lg font-semibold truncate">
                          {request.title}
                        </h3>
                        <div className="flex items-center gap-2 shrink-0">
                          <Badge
                            className={`${getPriorityColor(request.priority)}`}
                          >
                            {request.priority}
                          </Badge>
                          <Badge
                            variant="outline"
                            className="text-muted-foreground px-1.5"
                          >
                            {request.status === "completed" ? (
                              <CheckCircle className="fill-green-500 dark:fill-green-400" />
                            ) : request.status === "paused" ? (
                              <Pause className="fill-yellow-500 dark:fill-yellow-400" />
                            ) : (
                              <Loader className="animate-spin" />
                            )}
                            {request.status}
                          </Badge>
                        </div>
                      </div>

                      <div className="flex flex-col gap-1 hidden lg:flex">
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {request.description}
                        </p>

                        <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <DollarSign className="w-3 h-3" />
                            <span>
                              {request.currency}{" "}
                              {request.budgetMin.toLocaleString()} -{" "}
                              {request.budgetMax.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            <span>{request.location}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            <span>{formatTimeAgo(request.createdAt)}</span>
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-1 mt-3">
                          {request.tags.slice(0, 3).map(tag => (
                            <Badge
                              key={tag}
                              variant="outline"
                              className="text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                          {request.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{request.tags.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col gap-1 flex lg:hidden">
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      {request.description}
                    </p>

                    <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-3 h-3" />
                        <span>
                          {request.currency}{" "}
                          {request.budgetMin.toLocaleString()} -{" "}
                          {request.budgetMax.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span>{request.location}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{formatTimeAgo(request.createdAt)}</span>
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1 mt-3">
                      {request.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {request.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{request.tags.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Stats & Actions */}
                  <div className="flex lg:flex-col items-center lg:items-end gap-4 lg:gap-2 shrink-0">
                    <div className="flex items-center lg:items-end gap-3 lg:gap-3 text-sm">
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Eye className="w-4 h-4" />
                        <span>{request.viewCount}</span>
                      </div>
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <MessageSquare className="w-4 h-4" />
                        <span>{request.responseCount}</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Link href={`/dashboard/buyer/requests/${request.id}`}>
                        <Button
                          variant="outline"
                          size="sm"
                          className="gap-2 cursor-pointer"
                        >
                          View Details
                        </Button>
                      </Link>
                      {request.status === "active" && (
                        <Drawer direction={isMobile ? "bottom" : "right"}>
                          <DrawerTrigger asChild>
                            <Button
                              size="sm"
                              className="gap-2 custom-drawer-button cursor-pointer"
                            >
                              Responses ({request.responseCount})
                            </Button>
                          </DrawerTrigger>
                          <DrawerContent>
                            <SellerResponses />
                          </DrawerContent>
                        </Drawer>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </section>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RequestsContent />
    </Suspense>
  );
};

export default Page;
