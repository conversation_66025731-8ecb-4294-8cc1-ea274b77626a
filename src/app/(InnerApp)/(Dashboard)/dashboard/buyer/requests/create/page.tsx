"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Check,
  DollarSign,
  MapPin,
  Package,
  Settings,
  Upload,
} from "lucide-react";
import { RequestFormData } from "@/domain/buyer-requests/types";
import BasicInfoStep from "@/domain/buyer-requests/components/basic-info-step";
import RequirementsStep from "@/domain/buyer-requests/components/requirements-step";
import BudgetTimelineStep from "@/domain/buyer-requests/components/budget-timeline-step";
import LocationDeliveryStep from "@/domain/buyer-requests/components/location-delivery-step";
import AttachmentsFinalStep from "@/domain/buyer-requests/components/attachments-final-step";
import { WelcomeScreen } from "@/domain/buyer-requests/components/welcome-screen";

const TOTAL_STEPS = 5;

export default function NewRequestPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<Partial<RequestFormData>>({
    currency: "NGN",
    allowCounterOffers: true,
    isPrivate: false,
    autoAcceptBestOffer: false,
    tags: [],
    specifications: [],
    preferredBrands: [],
    referenceLinks: [],
    contactMethod: "platform",
  });

  const updateFormData = (data: Partial<RequestFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const handleNext = () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Here you would typically send the data to your API
      console.log("Submitting request:", formData);

      // Show success and redirect
      router.push("/dashboard/buyer/requests?success=true");
    } catch (error) {
      console.error("Error creating request:", error);
      // Handle error (show toast, etc.)
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return <WelcomeScreen onNext={handleNext} />;
      case 1:
        return (
          <BasicInfoStep
            data={formData}
            onUpdate={updateFormData}
            onNext={handleNext}
          />
        );
      case 2:
        return (
          <RequirementsStep
            data={formData}
            onUpdate={updateFormData}
            onNext={handleNext}
            onPrev={handlePrev}
          />
        );
      case 3:
        return (
          <BudgetTimelineStep
            data={formData}
            onUpdate={updateFormData}
            onNext={handleNext}
            onPrev={handlePrev}
          />
        );
      case 4:
        return (
          <LocationDeliveryStep
            data={formData}
            onUpdate={updateFormData}
            onNext={handleNext}
            onPrev={handlePrev}
          />
        );
      case 5:
        return (
          <AttachmentsFinalStep
            data={formData}
            onUpdate={updateFormData}
            onSubmit={handleSubmit}
            onPrev={handlePrev}
            isSubmitting={isSubmitting}
          />
        );
      default:
        return null;
    }
  };

  const STEPS = [
    {
      id: 1,
      title: "Basic Info",
      description: "What you need",
      icon: Package,
    },
    {
      id: 2,
      title: "Requirements",
      description: "Detailed specs",
      icon: Settings,
    },
    {
      id: 3,
      title: "Budget & Timeline",
      description: "Price & timing",
      icon: DollarSign,
    },
    {
      id: 4,
      title: "Location",
      description: "Delivery details",
      icon: MapPin,
    },
    {
      id: 5,
      title: "Attachments",
      description: "Final details",
      icon: Upload,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b">
        <div className="max-w-3xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4 mb-6 justify-between">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              Create New Request
            </h1>
            <span className="text-sm text-gray-500">
              Step {currentStep + 1} of {TOTAL_STEPS}
            </span>
          </div>
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-2">
              {STEPS.map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={index} className="flex items-center">
                    <div
                      className={`flex items-center justify-center w-8 h-8 rounded-full ${
                        index <= currentStep
                          ? "bg-black text-white"
                          : "bg-gray-200 text-gray-400"
                      }`}
                    >
                      {index < currentStep ? (
                        <Check className="w-4 h-4" />
                      ) : (
                        <Icon className="w-4 h-4" />
                      )}
                    </div>
                    {index < STEPS.length - 1 && (
                      <div
                        className={`w-8 h-0.5 ${index < currentStep ? "bg-black" : "bg-gray-200"}`}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8 flex justify-center">
        {renderCurrentStep()}
      </div>
    </div>
  );
}
