"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  TrendingUp,
  Eye,
  MessageSquare,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  Target,
  Award,
  Zap,
  ArrowUp,
  ArrowDown,
} from "lucide-react";

// Mock analytics data
const mockAnalytics = {
  overview: {
    totalRequests: 12,
    activeRequests: 5,
    completedRequests: 4,
    totalViews: 342,
    totalResponses: 67,
    averageResponseTime: "4.2 hours",
    successRate: 75,
    averageBudget: 2450,
  },
  trends: {
    viewsChange: 23,
    responsesChange: 15,
    completionChange: -8,
    budgetChange: 12,
  },
  categoryBreakdown: [
    { category: "Electronics", requests: 5, responses: 28, avgPrice: 1850 },
    { category: "Home & Garden", requests: 3, responses: 18, avgPrice: 3200 },
    { category: "Fashion", requests: 2, responses: 12, avgPrice: 450 },
    { category: "Health & Beauty", requests: 2, responses: 9, avgPrice: 280 },
  ],
  recentActivity: [
    {
      id: "ACT001",
      type: "new_response",
      title: "New response on Gaming Laptop request",
      description: "TechGear Pro responded with $1,650 offer",
      timestamp: "2 hours ago",
      status: "pending",
    },
    {
      id: "ACT002",
      type: "request_viewed",
      title: "Office Furniture request viewed",
      description: "5 new views in the last hour",
      timestamp: "3 hours ago",
      status: "active",
    },
    {
      id: "ACT003",
      type: "offer_accepted",
      title: "Camera Equipment offer accepted",
      description: "Accepted offer from PhotoPro Solutions",
      timestamp: "1 day ago",
      status: "completed",
    },
  ],
  topPerformingRequests: [
    {
      id: "REQ001",
      title: "Gaming Laptop",
      views: 45,
      responses: 8,
      avgOfferPrice: 1625,
      status: "active",
    },
    {
      id: "REQ003",
      title: "Camera Equipment",
      views: 67,
      responses: 15,
      avgOfferPrice: 4200,
      status: "completed",
    },
    {
      id: "REQ002",
      title: "Office Furniture",
      views: 32,
      responses: 12,
      avgOfferPrice: 6500,
      status: "active",
    },
  ],
};

export default function RequestAnalyticsPage() {
  const [timeRange, setTimeRange] = useState("30d");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const {
    overview,
    trends,
    categoryBreakdown,
    recentActivity,
    topPerformingRequests,
  } = mockAnalytics;

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "new_response":
        return <MessageSquare className="w-4 h-4 text-blue-500" />;
      case "request_viewed":
        return <Eye className="w-4 h-4 text-green-500" />;
      case "offer_accepted":
        return <CheckCircle className="w-4 h-4 text-purple-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <BarChart3 className="w-8 h-8 text-primary" />
            Request Analytics
          </h1>
          <p className="text-muted-foreground mt-1">
            Track your request performance and optimize your buying strategy
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="electronics">Electronics</SelectItem>
              <SelectItem value="home">Home & Garden</SelectItem>
              <SelectItem value="fashion">Fashion</SelectItem>
              <SelectItem value="health">Health & Beauty</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Requests</p>
                <p className="text-3xl font-bold">{overview.totalRequests}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              <Badge variant="outline" className="text-xs">
                {overview.activeRequests} active
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Views</p>
                <p className="text-3xl font-bold">{overview.totalViews}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Eye className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2 text-sm">
              {trends.viewsChange > 0 ? (
                <ArrowUp className="w-4 h-4 text-green-500" />
              ) : (
                <ArrowDown className="w-4 h-4 text-red-500" />
              )}
              <span
                className={
                  trends.viewsChange > 0 ? "text-green-600" : "text-red-600"
                }
              >
                {Math.abs(trends.viewsChange)}%
              </span>
              <span className="text-muted-foreground">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Responses</p>
                <p className="text-3xl font-bold">{overview.totalResponses}</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2 text-sm">
              <span className="text-muted-foreground">Avg:</span>
              <span className="font-medium">
                {Math.round(overview.totalResponses / overview.totalRequests)}{" "}
                per request
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Success Rate</p>
                <p className="text-3xl font-bold">{overview.successRate}%</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Target className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              <Badge variant="outline" className="text-xs">
                {overview.completedRequests} completed
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-3 gap-6 mb-8">
        {/* Category Breakdown */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-primary" />
              Category Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {categoryBreakdown.map((category, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <h4 className="font-semibold">{category.category}</h4>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                      <span>{category.requests} requests</span>
                      <span>•</span>
                      <span>{category.responses} responses</span>
                      <span>•</span>
                      <span>Avg: ${category.avgPrice.toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">
                      {Math.round(category.responses / category.requests)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      responses/request
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Insights */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-primary" />
              Quick Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <TrendingUp className="w-4 h-4 text-blue-600" />
                <span className="font-medium text-blue-800">
                  Peak Performance
                </span>
              </div>
              <p className="text-sm text-blue-700">
                Electronics requests get 40% more responses than average
              </p>
            </div>

            <div className="p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Clock className="w-4 h-4 text-green-600" />
                <span className="font-medium text-green-800">
                  Response Time
                </span>
              </div>
              <p className="text-sm text-green-700">
                Average seller response time: {overview.averageResponseTime}
              </p>
            </div>

            <div className="p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Award className="w-4 h-4 text-purple-600" />
                <span className="font-medium text-purple-800">
                  Best Category
                </span>
              </div>
              <p className="text-sm text-purple-700">
                Home & Garden has the highest completion rate (85%)
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-primary" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map(activity => (
                <div
                  key={activity.id}
                  className="flex items-start gap-3 p-3 border rounded-lg"
                >
                  <div className="mt-1">{getActivityIcon(activity.type)}</div>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{activity.title}</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      {activity.description}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-xs text-muted-foreground">
                        {activity.timestamp}
                      </span>
                      <Badge
                        className={`${getStatusColor(activity.status)} text-xs`}
                      >
                        {activity.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Performing Requests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5 text-primary" />
              Top Performing Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPerformingRequests.map((request, index) => (
                <div
                  key={request.id}
                  className="flex items-center gap-3 p-3 border rounded-lg"
                >
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-primary">
                      #{index + 1}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{request.title}</h4>
                    <div className="flex items-center gap-3 text-xs text-muted-foreground mt-1">
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        <span>{request.views}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageSquare className="w-3 h-3" />
                        <span>{request.responses}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-3 h-3" />
                        <span>${request.avgOfferPrice.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  <Badge
                    className={`${getStatusColor(request.status)} text-xs`}
                  >
                    {request.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
