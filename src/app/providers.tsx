"use client";

import { ProgressProvider } from "@bprogress/next/app";
import ReactQueryProviders from "@/lib/api/react-query/provider";

const Providers = ({ children }: { children: React.ReactNode }) => {
  return (
    <ProgressProvider
      height="4px"
      color="#3b82f6"
      options={{ showSpinner: false }}
      shallowRouting
    >
      <ReactQueryProviders>{children}</ReactQueryProviders>
    </ProgressProvider>
  );
};

export default Providers;
